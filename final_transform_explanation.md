# 最终修正：摄像头原地旋转的变换矩阵

## 正确的几何理解

您的理解完全正确：
- **摄像头是在原地旋转的**
- **位置固定为 [0.100, 0.000, -1.000]**
- **只有姿态（pitch、yaw、roll角度）在变化**

## 几何参数

- **摄像头位置**：[0.100, 0.000, -1.000]（固定不变）
- **Pitch角度**：17°（向上倾斜为正，上下旋转）
- **Yaw角度**：-2°（左右旋转）
- **Roll角度**：0°（滚转角）

## 距离验证

- **垂直距离**：|Z| = 1.000米 ✓
- **水平偏移**：X = 0.100米 ✓
- **Y坐标**：0.000米（摄像头在原地旋转）✓
- **到墙原点直线距离**：√(0.1² + 0² + 1²) = 1.005米

## 最终变换矩阵

### 4×4齐次变换矩阵 T_wall_to_cam

```
[ 0.99939,  0.00000,  0.03490, -0.06504]
[-0.01020,  0.95630,  0.29219,  0.29237]
[-0.03337, -0.29237,  0.95572,  0.95572]
[ 0.00000,  0.00000,  0.00000,  1.00000]
```

### 计算过程

1. **旋转矩阵**：R = Rz(0°) × Ry(-2°) × Rx(17°)
2. **平移向量**：t = -R × [0.1, 0, -1]
3. **变换矩阵**：T = [R | t; 0 0 0 1]

## 测试验证

### 墙原点变换
墙原点 [0, 0, 0] → 摄像头坐标系 [-0.065, 0.292, 0.956]

**物理意义**：
- **X = -0.065**：由于yaw角-2°和水平偏移的影响
- **Y = 0.292**：由于pitch角17°向上倾斜的影响
- **Z = 0.956**：墙原点在光轴方向上的投影距离

### 墙上一点变换
墙上点 [0.5, 0.3, 0] → 摄像头坐标系 [0.435, 0.575, 0.851]

## 关键优势

1. **符合物理实际**：摄像头确实是原地旋转的
2. **几何关系正确**：位置固定，只有姿态变化
3. **计算简洁**：避免了复杂的位置补偿计算
4. **易于理解**：直观的几何关系

## 应用场景

这种建模方式适用于：
- 摄像头云台控制
- 机器视觉标定
- 三维重建
- 目标跟踪

摄像头在固定位置进行pitch和yaw旋转，这是最常见和最实用的配置。

## 总结

修正后的变换矩阵基于正确的几何假设：
- 摄像头位置固定：[0.100, 0.000, -1.000]
- 只有姿态角度变化：pitch=17°, yaw=-2°
- 符合实际摄像头工作原理

这个变换矩阵可以准确地将墙面坐标系中的任意点转换到摄像头坐标系中。
