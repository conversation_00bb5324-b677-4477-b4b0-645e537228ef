"""
解释为什么墙原点在摄像头坐标系下Z值小于实际距离1米
"""

import numpy as np
import math

def rot_x(deg: float) -> np.ndarray:
    a = math.radians(deg)
    ca, sa = math.cos(a), math.sin(a)
    return np.array([[1,0,0],[0,ca,-sa],[0,sa,ca]], dtype=np.float64)

def rot_y(deg: float) -> np.ndarray:
    a = math.radians(deg)
    ca, sa = math.cos(a), math.sin(a)
    return np.array([[ca,0,sa],[0,1,0],[-sa,0,ca]], dtype=np.float64)

def rot_z(deg: float) -> np.ndarray:
    a = math.radians(deg)
    ca, sa = math.cos(a), math.sin(a)
    return np.array([[ca,-sa,0],[sa,ca,0],[0,0,1]], dtype=np.float64)

def build_rotation(pitch, yaw, roll):
    return rot_z(roll) @ rot_y(yaw) @ rot_x(pitch)

def explain_z_distance():
    """详细解释Z距离的几何关系"""
    
    print("=" * 70)
    print("为什么墙原点在摄像头坐标系下Z值小于1米？")
    print("=" * 70)
    
    # 参数
    pitch_cam = 17.0
    yaw_cam = -2.0
    distance_to_wall = 1.0
    horizontal_offset = 0.1
    
    # 摄像头在墙坐标系中的位置
    C_cam_in_wall = np.array([horizontal_offset, 0.0, -distance_to_wall])
    
    # 摄像头姿态
    R_cam_to_wall = build_rotation(pitch_cam, yaw_cam, 0.0)
    R_wall_to_cam = R_cam_to_wall.T
    
    # 墙原点在摄像头坐标系中的位置
    wall_origin = np.array([0.0, 0.0, 0.0])
    wall_origin_in_cam = R_wall_to_cam @ (wall_origin - C_cam_in_wall)
    
    print(f"给定参数：")
    print(f"  摄像头pitch角度: {pitch_cam}°")
    print(f"  摄像头yaw角度: {yaw_cam}°")
    print(f"  摄像头到墙距离: {distance_to_wall}米")
    print(f"  水平偏移: {horizontal_offset}米")
    
    print(f"\n摄像头在墙坐标系中的位置：")
    print(f"  C_cam = [{C_cam_in_wall[0]:.3f}, {C_cam_in_wall[1]:.3f}, {C_cam_in_wall[2]:.3f}]")
    
    print(f"\n墙原点在摄像头坐标系中的位置：")
    print(f"  P_cam = [{wall_origin_in_cam[0]:.3f}, {wall_origin_in_cam[1]:.3f}, {wall_origin_in_cam[2]:.3f}]")
    print(f"  Z坐标 = {wall_origin_in_cam[2]:.3f}米")
    
    # 几何分析
    print(f"\n几何分析：")
    
    # 1. 直线距离
    direct_distance = np.linalg.norm(wall_origin - C_cam_in_wall)
    print(f"  1. 摄像头光心到墙原点的直线距离: {direct_distance:.3f}米")
    
    # 2. 光轴方向的投影
    # 摄像头光轴在墙坐标系中的方向
    cam_optical_axis_in_wall = R_cam_to_wall @ np.array([0, 0, 1])
    print(f"  2. 摄像头光轴在墙坐标系中的方向: [{cam_optical_axis_in_wall[0]:.3f}, {cam_optical_axis_in_wall[1]:.3f}, {cam_optical_axis_in_wall[2]:.3f}]")
    
    # 3. 墙原点到摄像头的向量
    vector_to_wall = wall_origin - C_cam_in_wall
    print(f"  3. 从摄像头到墙原点的向量: [{vector_to_wall[0]:.3f}, {vector_to_wall[1]:.3f}, {vector_to_wall[2]:.3f}]")
    
    # 4. 在光轴方向上的投影长度
    projection_on_optical_axis = np.dot(vector_to_wall, cam_optical_axis_in_wall)
    print(f"  4. 在光轴方向上的投影长度: {projection_on_optical_axis:.3f}米")
    
    # 5. 角度分析
    cos_angle = projection_on_optical_axis / direct_distance
    angle_deg = math.degrees(math.acos(cos_angle))
    print(f"  5. 摄像头光轴与直线距离的夹角: {angle_deg:.2f}°")
    
    print(f"\n关键理解：")
    print(f"  • 摄像头坐标系的Z轴是沿着光轴方向")
    print(f"  • 由于摄像头有17°的pitch角，光轴不是垂直指向墙面")
    print(f"  • Z坐标表示的是点在光轴方向上的距离，不是直线距离")
    print(f"  • 实际计算：1.0米 × cos(17°) ≈ {1.0 * math.cos(math.radians(17)):.3f}米")
    
    # 验证计算
    theoretical_z = 1.0 * math.cos(math.radians(17))
    print(f"\n验证：")
    print(f"  理论Z值（忽略yaw和偏移）: {theoretical_z:.3f}米")
    print(f"  实际Z值: {wall_origin_in_cam[2]:.3f}米")
    print(f"  差异主要由yaw角(-2°)和水平偏移(0.1m)引起")

def visualize_geometry():
    """可视化几何关系"""
    print(f"\n" + "="*50)
    print("几何关系示意图（侧视图，忽略yaw角）")
    print("="*50)
    
    print("""
    墙面 (Z=0)     摄像头 (Z=-1)
        |              /
        |           /  ← 17° pitch角
        |        /
        |     /
        |  /
        |/
    ----+---- 墙原点
        
    在摄像头坐标系中：
    - Z轴沿光轴方向
    - 墙原点的Z坐标 = 距离 × cos(pitch角)
    - Z = 1.0m × cos(17°) ≈ 0.956m
    """)

if __name__ == "__main__":
    explain_z_distance()
    visualize_geometry()
