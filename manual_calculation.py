"""
手动计算修正后的变换矩阵
摄像头位置：[0.100, 0.000, -1.000]
"""

import numpy as np
import math

# 计算旋转矩阵
def calculate_rotation_matrices():
    pitch = 17.0  # 度
    yaw = -2.0    # 度
    roll = 0.0    # 度
    
    # 转换为弧度
    pitch_rad = math.radians(pitch)
    yaw_rad = math.radians(yaw)
    roll_rad = math.radians(roll)
    
    # 计算三角函数值
    cp, sp = math.cos(pitch_rad), math.sin(pitch_rad)
    cy, sy = math.cos(yaw_rad), math.sin(yaw_rad)
    cr, sr = math.cos(roll_rad), math.sin(roll_rad)
    
    print(f"Pitch = {pitch}°, cos = {cp:.6f}, sin = {sp:.6f}")
    print(f"Yaw = {yaw}°, cos = {cy:.6f}, sin = {sy:.6f}")
    print(f"Roll = {roll}°, cos = {cr:.6f}, sin = {sr:.6f}")
    
    # Rx(pitch)
    Rx = np.array([
        [1, 0, 0],
        [0, cp, -sp],
        [0, sp, cp]
    ])
    
    # Ry(yaw)
    Ry = np.array([
        [cy, 0, sy],
        [0, 1, 0],
        [-sy, 0, cy]
    ])
    
    # Rz(roll)
    Rz = np.array([
        [cr, -sr, 0],
        [sr, cr, 0],
        [0, 0, 1]
    ])
    
    # R_cam_to_wall = Rz * Ry * Rx
    R_cam_to_wall = Rz @ Ry @ Rx
    
    print(f"\nR_cam_to_wall:")
    for i in range(3):
        print(f"[{R_cam_to_wall[i,0]:8.5f}, {R_cam_to_wall[i,1]:8.5f}, {R_cam_to_wall[i,2]:8.5f}]")
    
    # R_wall_to_cam = R_cam_to_wall^T
    R_wall_to_cam = R_cam_to_wall.T
    
    print(f"\nR_wall_to_cam:")
    for i in range(3):
        print(f"[{R_wall_to_cam[i,0]:8.5f}, {R_wall_to_cam[i,1]:8.5f}, {R_wall_to_cam[i,2]:8.5f}]")
    
    return R_wall_to_cam

def calculate_transform():
    # 摄像头位置（修正后）
    C_cam = np.array([0.100, 0.000, -1.000])
    
    print(f"摄像头位置: [{C_cam[0]:.3f}, {C_cam[1]:.3f}, {C_cam[2]:.3f}]")
    
    # 验证距离
    perpendicular_dist = abs(C_cam[2])
    direct_distance = math.sqrt(C_cam[0]**2 + C_cam[1]**2 + C_cam[2]**2)
    print(f"垂直距离: {perpendicular_dist:.3f}米")
    print(f"直线距离: {direct_distance:.3f}米")
    
    # 计算旋转矩阵
    R_wall_to_cam = calculate_rotation_matrices()
    
    # 计算平移向量
    t_wall_to_cam = -R_wall_to_cam @ C_cam
    
    print(f"\n平移向量 t_wall_to_cam:")
    print(f"[{t_wall_to_cam[0]:8.5f}, {t_wall_to_cam[1]:8.5f}, {t_wall_to_cam[2]:8.5f}]")
    
    # 构建4x4变换矩阵
    T_wall_to_cam = np.eye(4)
    T_wall_to_cam[:3, :3] = R_wall_to_cam
    T_wall_to_cam[:3, 3] = t_wall_to_cam
    
    print(f"\n完整变换矩阵 T_wall_to_cam:")
    for i in range(4):
        print(f"[{T_wall_to_cam[i,0]:8.5f}, {T_wall_to_cam[i,1]:8.5f}, {T_wall_to_cam[i,2]:8.5f}, {T_wall_to_cam[i,3]:8.5f}]")
    
    # 测试变换
    print(f"\n测试点变换：")
    
    # 墙原点
    P_wall_origin = np.array([0, 0, 0, 1])
    P_cam_origin = T_wall_to_cam @ P_wall_origin
    print(f"墙原点 [0,0,0] -> 摄像头坐标系: [{P_cam_origin[0]:.3f}, {P_cam_origin[1]:.3f}, {P_cam_origin[2]:.3f}]")
    
    # 墙上一点
    P_wall_point = np.array([0.5, 0.3, 0, 1])
    P_cam_point = T_wall_to_cam @ P_wall_point
    print(f"墙上点 [0.5,0.3,0] -> 摄像头坐标系: [{P_cam_point[0]:.3f}, {P_cam_point[1]:.3f}, {P_cam_point[2]:.3f}]")

if __name__ == "__main__":
    calculate_transform()
