# 基于墙面角点的摄像头内参标定指南

## 概述

本项目提供了一套完整的摄像头内参标定解决方案，基于墙面角点的3D-2D对应关系。与传统的棋盘格标定方法不同，这种方法使用墙面上的特征点进行标定，适用于大尺度场景和固定安装的摄像头。

## 核心原理

### 1. 坐标系变换
- **墙面坐标系**: 以墙面为基准建立的3D坐标系
- **摄像头坐标系**: 以摄像头光心为原点的3D坐标系
- **图像坐标系**: 2D像素坐标系

### 2. 标定流程
1. 获取墙面角点的3D坐标（墙面坐标系）
2. 检测对应角点在图像中的2D坐标
3. 使用已知的墙面到摄像头变换矩阵
4. 通过3D-2D对应关系求解摄像头内参

## 文件结构

```
├── calculate_wall_to_camera_transform.py  # 变换矩阵计算
├── camera_intrinsic_calibration.py       # 内参标定核心功能
├── calibration_example.py                # 实际使用示例
├── camera_calibration_guide.md           # 本使用指南
└── 输出文件/
    ├── camera_intrinsic_params.npz       # 标定参数
    ├── calibration_results.png           # 可视化结果
    └── sample_corner_data.txt             # 示例数据
```

## 使用方法

### 方法1：使用测试数据（快速验证）

```python
from camera_intrinsic_calibration import main
main()  # 运行完整的测试流程
```

### 方法2：使用真实数据

#### 步骤1：准备角点数据
创建 `corner_data.txt` 文件，格式如下：
```
# x_wall y_wall z_wall x_image y_image
-0.6  0.4  0.0  856.2  324.7
-0.3  0.4  0.0  1024.5 318.9
 0.0  0.4  0.0  1192.8 313.1
 0.3  0.4  0.0  1361.1 307.3
...
```

其中：
- `x_wall, y_wall, z_wall`: 角点在墙面坐标系中的3D坐标（米）
- `x_image, y_image`: 角点在图像中的2D坐标（像素）

#### 步骤2：执行标定
```python
from calibration_example import calibrate_from_real_data
camera_matrix, dist_coeffs, error = calibrate_from_real_data()
```

#### 步骤3：查看结果
标定完成后会生成：
- 内参矩阵和畸变系数
- 重投影误差统计
- 可视化图表
- 保存的参数文件

## 关键参数说明

### 摄像头几何参数
```python
# 在 calculate_wall_to_camera_transform.py 中设置
pitch_cam = 17.0    # 摄像头pitch角度（向上倾斜为正）
yaw_cam = -2.0      # 摄像头yaw角度（左右旋转）
roll_cam = 0.0      # 摄像头roll角度（滚转角）

# 几何约束
perpendicular_distance = 1.0    # 摄像头到墙的垂直距离（米）
horizontal_offset = 0.1         # 水平偏移（米）
```

### 标定参数
```python
# 图像尺寸
image_size = (1920, 1080)  # (width, height)

# 初始内参猜测（可选）
fx = fy = max(image_size) * 0.8  # 初始焦距估计
cx, cy = image_size[0] / 2, image_size[1] / 2  # 主点位置
```

## 标定质量评估

### 重投影误差标准
- **优秀**: 平均误差 < 1.0 pixel
- **良好**: 平均误差 < 2.0 pixels  
- **一般**: 平均误差 < 5.0 pixels
- **较差**: 平均误差 ≥ 5.0 pixels

### 提高标定精度的建议
1. **增加角点数量**: 建议至少20个角点
2. **改善角点分布**: 角点应均匀分布在图像中
3. **提高检测精度**: 使用亚像素级角点检测
4. **减少噪声**: 确保图像清晰，角点检测准确
5. **验证几何参数**: 确保摄像头姿态参数准确

## 输出结果

### 内参矩阵格式
```
camera_matrix = [
    [fx,  0, cx],
    [ 0, fy, cy],
    [ 0,  0,  1]
]
```

其中：
- `fx, fy`: X和Y方向的焦距（像素）
- `cx, cy`: 主点坐标（像素）

### 畸变系数
```
dist_coeffs = [k1, k2, p1, p2, k3]
```

其中：
- `k1, k2, k3`: 径向畸变系数
- `p1, p2`: 切向畸变系数

## 应用场景

1. **监控摄像头标定**: 固定安装的监控摄像头
2. **机器视觉系统**: 工业检测中的摄像头标定
3. **三维重建**: 需要精确内参的3D重建应用
4. **增强现实**: AR应用中的摄像头标定
5. **机器人视觉**: 移动机器人的视觉系统标定

## 注意事项

1. **坐标系一致性**: 确保墙面3D坐标和摄像头几何参数使用相同的坐标系
2. **角点质量**: 角点检测要准确，建议使用亚像素级检测
3. **几何约束**: 摄像头的实际安装参数要与代码中的参数一致
4. **数据有效性**: 确保所有角点都在摄像头视野内且在墙面前方

## 故障排除

### 常见问题
1. **标定失败**: 检查角点数据格式和数量
2. **误差过大**: 验证几何参数和角点检测精度
3. **结果不合理**: 检查坐标系定义和变换矩阵

### 调试建议
1. 使用测试数据验证算法正确性
2. 可视化角点分布和重投影结果
3. 逐步增加角点数量观察误差变化
4. 对比不同参数设置的标定结果
