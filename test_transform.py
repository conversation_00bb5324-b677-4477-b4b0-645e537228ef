"""
测试修正后的变换矩阵计算
"""

import numpy as np
import math

def rot_x(deg: float) -> np.ndarray:
    a = math.radians(deg)
    ca, sa = math.cos(a), math.sin(a)
    return np.array([[1,0,0],[0,ca,-sa],[0,sa,ca]], dtype=np.float64)

def rot_y(deg: float) -> np.ndarray:
    a = math.radians(deg)
    ca, sa = math.cos(a), math.sin(a)
    return np.array([[ca,0,sa],[0,1,0],[-sa,0,ca]], dtype=np.float64)

def rot_z(deg: float) -> np.ndarray:
    a = math.radians(deg)
    ca, sa = math.cos(a), math.sin(a)
    return np.array([[ca,-sa,0],[sa,ca,0],[0,0,1]], dtype=np.float64)

def build_rotation(pitch, yaw, roll):
    return rot_z(roll) @ rot_y(yaw) @ rot_x(pitch)

def calculate_transform():
    # 摄像头参数
    pitch_cam = 17.0    # 向上倾斜为正
    yaw_cam = -2.0      # 左右旋转
    roll_cam = 0.0      # 滚转角
    
    # 几何参数
    perpendicular_distance = 1.0    # 垂直距离
    horizontal_offset = 0.1         # 水平偏移
    
    # 摄像头位置（固定，只有姿态变化）
    C_cam_in_wall = np.array([horizontal_offset, 0.0, -perpendicular_distance])
    
    # 摄像头姿态
    R_cam_to_wall = build_rotation(pitch_cam, yaw_cam, roll_cam)
    R_wall_to_cam = R_cam_to_wall.T
    
    # 平移向量
    t_wall_to_cam = -R_wall_to_cam @ C_cam_in_wall
    
    # 4x4变换矩阵
    T_wall_to_cam = np.eye(4)
    T_wall_to_cam[:3, :3] = R_wall_to_cam
    T_wall_to_cam[:3, 3] = t_wall_to_cam
    
    return T_wall_to_cam, R_wall_to_cam, t_wall_to_cam, C_cam_in_wall

if __name__ == "__main__":
    T, R, t, C_cam = calculate_transform()
    
    print("摄像头位置（修正版）：")
    print(f"C_cam = [{C_cam[0]:.3f}, {C_cam[1]:.3f}, {C_cam[2]:.3f}]")
    
    # 验证距离
    wall_origin = np.array([0.0, 0.0, 0.0])
    perpendicular_dist = abs(C_cam[2])
    direct_distance = np.linalg.norm(C_cam - wall_origin)
    print(f"垂直距离: {perpendicular_dist:.3f}米")
    print(f"直线距离: {direct_distance:.3f}米")
    
    print(f"\n变换矩阵 T_wall_to_cam:")
    for i in range(4):
        print(f"[{T[i,0]:8.5f}, {T[i,1]:8.5f}, {T[i,2]:8.5f}, {T[i,3]:8.5f}]")
    
    # 测试变换
    print(f"\n测试点变换：")
    P_wall_origin = np.array([0, 0, 0, 1])
    P_cam_origin = T @ P_wall_origin
    print(f"墙原点 [0,0,0] -> 摄像头坐标系: [{P_cam_origin[0]:.3f}, {P_cam_origin[1]:.3f}, {P_cam_origin[2]:.3f}]")
