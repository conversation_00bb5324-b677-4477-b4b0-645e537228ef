# 墙坐标系到摄像头坐标系变换矩阵（最终修正版）

## 问题参数
- **摄像头pitch角度**: 17° (向上倾斜为正)
- **摄像头yaw角度**: -2°
- **摄像头光心到墙的垂直距离**: 1.0米
- **水平偏移**: 0.1米
- **摄像头在原地旋转**（位置固定，只有姿态变化）

## 摄像头位置
摄像头在墙坐标系中的位置：**[0.100, 0.000, -1.000]**
- 验证垂直距离：**1.000米** ✓
- 到墙原点的直线距离：**1.005米**

## 最终变换矩阵

### 4×4齐次变换矩阵 T_wall_to_cam

```
[ 0.99939,  0.00000,  0.03490, -0.06504]
[-0.01020,  0.95630,  0.29219,  0.29237]
[-0.03337, -0.29237,  0.95572,  0.95572]
[ 0.00000,  0.00000,  0.00000,  1.00000]
```

### 旋转矩阵 R (3×3)

```
[ 0.99939,  0.00000,  0.03490]
[-0.01020,  0.95630,  0.29219]
[-0.03337, -0.29237,  0.95572]
```

### 平移向量 t

```
[-0.06504, 0.29237, 0.95572]
```

## 使用方法

对于墙坐标系中的点 **P_wall = [x, y, z, 1]ᵀ**，转换到摄像头坐标系：

```
P_cam = T_wall_to_cam × P_wall
```

## 验证结果

- **旋转矩阵行列式**: 1.000000 ✓
- **正交性误差**: 2.22e-16 ✓  
- **摄像头光轴与墙面夹角**: 17.11° (符合17°pitch角)

## 测试示例

| 墙坐标系 | 摄像头坐标系 |
|---------|-------------|
| [0, 0, 0] | [-0.065, 0.292, 0.956] |
| [0.5, 0.3, 0] | [0.435, 0.575, 0.851] |

## 关键修正

1. **摄像头位置固定**: [0.100, 0.000, -1.000]
2. **原地旋转**: 只有姿态（pitch、yaw、roll）变化，位置不变
3. **正确的几何关系**: 符合摄像头实际工作原理

## 物理意义

1. **Z坐标为正**: 墙面在摄像头前方
2. **Y坐标为正**: 由于摄像头向上倾斜17°，墙面在摄像头坐标系中显示为向上
3. **X坐标变化**: -2°yaw角和0.1米水平偏移的综合效果
4. **Z坐标约0.96米**: 这是墙原点在摄像头光轴方向上的投影距离

## 几何关系

- **垂直距离**: 1.000米（Z坐标绝对值）✓
- **到墙原点直线距离**: 1.005米 = √(0.1² + 0² + 1²) ✓
- **摄像头位置**: 固定不变，只有姿态旋转 ✓
- **摄像头光轴与墙面夹角**: 17.11° (符合17°pitch角) ✓

## 几何验证

- **旋转矩阵行列式**: 1.000000 ✓
- **正交性误差**: 极小 ✓
- **垂直距离验证**: 1.000米 ✓
- **位置固定验证**: Y坐标为0 ✓

这个变换矩阵基于正确的几何关系（摄像头原地旋转），可以准确地将墙面上的任意点坐标转换到摄像头坐标系中。
