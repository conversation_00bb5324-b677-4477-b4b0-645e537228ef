"""
摄像头内参标定实际使用示例
展示如何使用真实的墙面角点数据进行标定
"""

import numpy as np
import cv2
from camera_intrinsic_calibration import calibrate_camera_intrinsics, visualize_calibration_results
from calculate_wall_to_camera_transform import calculate_wall_to_camera_transform

def load_corner_data_from_file(file_path: str):
    """
    从文件加载角点数据
    文件格式：每行包含 x_wall, y_wall, z_wall, x_image, y_image
    """
    data = np.loadtxt(file_path)
    wall_points_3d = data[:, :3]  # 前3列是墙面3D坐标
    image_points_2d = data[:, 3:5]  # 后2列是图像2D坐标
    return wall_points_3d, image_points_2d

def create_sample_corner_data():
    """
    创建示例角点数据文件
    模拟在墙面上检测到的角点及其在图像中的位置
    """
    print("创建示例角点数据...")
    
    # 模拟墙面上的规则网格角点
    wall_points = []
    image_points = []
    
    # 假设墙面上有一个6x4的角点网格
    grid_cols, grid_rows = 6, 4
    wall_width, wall_height = 1.2, 0.8  # 墙面区域尺寸(米)
    
    # 生成墙面3D点
    for row in range(grid_rows):
        for col in range(grid_cols):
            # 墙面坐标系：原点在墙面中心，X向右，Y向上，Z=0
            x_wall = (col - (grid_cols-1)/2) * (wall_width / (grid_cols-1))
            y_wall = (row - (grid_rows-1)/2) * (wall_height / (grid_rows-1))
            z_wall = 0.0
            wall_points.append([x_wall, y_wall, z_wall])
    
    wall_points = np.array(wall_points)
    
    # 使用理想投影模型生成对应的图像点
    # 假设的摄像头内参
    fx, fy = 1200.0, 1200.0
    cx, cy = 960.0, 540.0
    
    # 获取变换矩阵
    T_wall_to_cam, _, _, _ = calculate_wall_to_camera_transform()
    
    # 转换到摄像头坐标系
    wall_points_homo = np.hstack([wall_points, np.ones((wall_points.shape[0], 1))])
    cam_points_homo = (T_wall_to_cam @ wall_points_homo.T).T
    cam_points = cam_points_homo[:, :3]
    
    # 投影到图像平面
    for point_3d in cam_points:
        if point_3d[2] > 0:  # 确保在摄像头前方
            x_img = fx * point_3d[0] / point_3d[2] + cx
            y_img = fy * point_3d[1] / point_3d[2] + cy
            # 添加一些噪声模拟实际检测误差
            x_img += np.random.normal(0, 0.5)
            y_img += np.random.normal(0, 0.5)
            image_points.append([x_img, y_img])
        else:
            image_points.append([0, 0])  # 无效点
    
    image_points = np.array(image_points)
    
    # 保存到文件
    data = np.hstack([wall_points, image_points])
    np.savetxt('sample_corner_data.txt', data, 
               header='x_wall y_wall z_wall x_image y_image', 
               fmt='%.6f')
    
    print(f"已创建示例数据文件 'sample_corner_data.txt'，包含 {len(wall_points)} 个角点")
    return wall_points, image_points

def calibrate_from_real_data():
    """
    使用真实角点数据进行标定
    """
    print("\n" + "="*60)
    print("基于真实角点数据的摄像头内参标定")
    print("="*60)
    
    # 1. 尝试加载真实数据，如果不存在则创建示例数据
    try:
        wall_points_3d, image_points_2d = load_corner_data_from_file('corner_data.txt')
        print("已加载真实角点数据文件 'corner_data.txt'")
    except (FileNotFoundError, OSError):
        print("未找到 'corner_data.txt'，使用示例数据...")
        wall_points_3d, image_points_2d = create_sample_corner_data()
    
    print(f"加载了 {len(wall_points_3d)} 个角点对应关系")
    
    # 2. 设置图像尺寸（根据实际摄像头调整）
    image_size = (1920, 1080)
    
    # 3. 执行标定
    print("\n执行摄像头内参标定...")
    try:
        camera_matrix, dist_coeffs, reprojection_error = calibrate_camera_intrinsics(
            wall_points_3d, image_points_2d, image_size
        )
        
        print(f"标定成功！重投影误差: {reprojection_error:.3f} pixels")
        
        # 4. 可视化结果
        T_wall_to_cam, _, _, _ = calculate_wall_to_camera_transform()
        visualize_calibration_results(
            wall_points_3d, image_points_2d, camera_matrix, dist_coeffs, image_size, T_wall_to_cam
        )
        
        # 5. 保存结果
        np.savez('real_camera_params.npz',
                 camera_matrix=camera_matrix,
                 dist_coeffs=dist_coeffs,
                 reprojection_error=reprojection_error,
                 image_size=image_size,
                 wall_points_3d=wall_points_3d,
                 image_points_2d=image_points_2d)
        print("\n标定结果已保存到 'real_camera_params.npz'")
        
        return camera_matrix, dist_coeffs, reprojection_error
        
    except Exception as e:
        print(f"标定失败: {e}")
        return None, None, None

def validate_calibration_accuracy(camera_matrix, dist_coeffs):
    """
    验证标定精度
    """
    print("\n" + "="*40)
    print("标定精度验证")
    print("="*40)
    
    # 加载标定数据
    try:
        data = np.load('real_camera_params.npz')
        wall_points_3d = data['wall_points_3d']
        image_points_2d = data['image_points_2d']
        
        # 计算重投影误差的详细统计
        T_wall_to_cam, _, _, _ = calculate_wall_to_camera_transform()
        
        # 转换到摄像头坐标系
        wall_points_homo = np.hstack([wall_points_3d, np.ones((wall_points_3d.shape[0], 1))])
        cam_points_homo = (T_wall_to_cam @ wall_points_homo.T).T
        cam_points = cam_points_homo[:, :3]
        
        # 重投影
        rvec = np.zeros(3)
        tvec = np.zeros(3)
        reprojected_points, _ = cv2.projectPoints(
            cam_points.astype(np.float32), rvec, tvec, camera_matrix, dist_coeffs
        )
        reprojected_points = reprojected_points.reshape(-1, 2)
        
        # 计算误差
        errors = np.sqrt(np.sum((image_points_2d - reprojected_points) ** 2, axis=1))
        
        print(f"重投影误差统计:")
        print(f"  平均误差: {np.mean(errors):.3f} pixels")
        print(f"  中位数误差: {np.median(errors):.3f} pixels")
        print(f"  最大误差: {np.max(errors):.3f} pixels")
        print(f"  最小误差: {np.min(errors):.3f} pixels")
        print(f"  标准差: {np.std(errors):.3f} pixels")
        print(f"  95%分位数: {np.percentile(errors, 95):.3f} pixels")
        
        # 评估标定质量
        if np.mean(errors) < 1.0:
            print("\n标定质量: 优秀 (平均误差 < 1.0 pixel)")
        elif np.mean(errors) < 2.0:
            print("\n标定质量: 良好 (平均误差 < 2.0 pixels)")
        elif np.mean(errors) < 5.0:
            print("\n标定质量: 一般 (平均误差 < 5.0 pixels)")
        else:
            print("\n标定质量: 较差 (平均误差 >= 5.0 pixels)")
            print("建议：增加角点数量或改善角点分布")
            
    except FileNotFoundError:
        print("未找到标定数据文件")

if __name__ == "__main__":
    # 执行标定
    camera_matrix, dist_coeffs, error = calibrate_from_real_data()
    
    if camera_matrix is not None:
        # 验证标定精度
        validate_calibration_accuracy(camera_matrix, dist_coeffs)
        
        print("\n" + "="*60)
        print("使用说明:")
        print("1. 将真实的角点数据保存为 'corner_data.txt' 文件")
        print("2. 文件格式：每行包含 x_wall y_wall z_wall x_image y_image")
        print("3. 运行此脚本进行标定")
        print("4. 标定结果保存在 'real_camera_params.npz' 中")
        print("="*60)
