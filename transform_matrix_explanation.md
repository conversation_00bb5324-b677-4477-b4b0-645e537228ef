# 从墙坐标系到摄像头坐标系的变换矩阵计算

## 问题描述

假设摄像头的前方有一面墙，需要计算从墙的三维坐标系到摄像头三维坐标系的变换矩阵。

**给定参数：**
- 摄像头pitch角度：17°
- 摄像头yaw角度：-2°
- 摄像头和墙的距离：1米
- 摄像头中心和墙的三维坐标系原点的水平距离：0.1米

## 坐标系定义

### 墙坐标系（世界坐标系）
- 原点：墙面上的参考点
- X轴：水平向右
- Y轴：垂直向上
- Z轴：垂直墙面向外（右手坐标系）

### 摄像头坐标系
- 原点：摄像头光心
- X轴：图像平面水平向右
- Y轴：图像平面垂直向下
- Z轴：光轴方向向前（右手坐标系）

## 数学推导

### 1. 摄像头在墙坐标系中的位置

根据给定条件：
```
C_cam = [0.1, 0.0, -1.0]  (单位：米)
```

### 2. 摄像头的姿态矩阵

摄像头的旋转角度：
- Pitch = 17° (绕X轴旋转)
- Yaw = -2° (绕Y轴旋转)  
- Roll = 0° (绕Z轴旋转)

旋转矩阵计算：R = Rz(roll) × Ry(yaw) × Rx(pitch)

```
Rx(17°) = [1    0      0   ]
          [0  cos17° -sin17°]
          [0  sin17°  cos17°]

Ry(-2°) = [cos(-2°)  0  sin(-2°)]
          [0         1  0       ]
          [-sin(-2°) 0  cos(-2°)]

Rz(0°) = [1  0  0]
         [0  1  0]
         [0  0  1]
```

### 3. 从墙坐标系到摄像头坐标系的变换

**旋转部分：**
```
R_wall_to_cam = R_cam_to_wall^T
```

**平移部分：**
```
t_wall_to_cam = -R_wall_to_cam × C_cam
```

## 计算结果

### 变换矩阵 T_wall_to_cam (4×4)

```
[ 0.99939,  0.00000,  0.03490, -0.06504]
[-0.01020,  0.95630,  0.29219,  0.29321]
[-0.03337, -0.29237,  0.95572,  0.95906]
[ 0.00000,  0.00000,  0.00000,  1.00000]
```

### 旋转矩阵 R_wall_to_cam (3×3)

```
[ 0.99939,  0.00000,  0.03490]
[-0.01020,  0.95630,  0.29219]
[-0.03337, -0.29237,  0.95572]
```

### 平移向量 t_wall_to_cam

```
[-0.06504, 0.29321, 0.95906]
```

## 使用方法

对于墙坐标系中的任意点 P_wall = [x, y, z, 1]^T，可以通过以下公式转换到摄像头坐标系：

```
P_cam = T_wall_to_cam × P_wall
```

## 验证测试

### 测试点1：墙坐标系原点 [0, 0, 0]
转换后在摄像头坐标系中的坐标：[-0.065, 0.293, 0.959]

### 测试点2：墙上一点 [0.5, 0.3, 0]  
转换后在摄像头坐标系中的坐标：[0.435, 0.575, 0.855]

## 物理意义解释

1. **Z坐标为正值**：表明墙面在摄像头前方，符合预期
2. **Y坐标为正值**：由于摄像头有17°的pitch角向下倾斜，墙面在摄像头坐标系中显示为向上偏移
3. **X坐标变化**：由于摄像头有-2°的yaw角和0.1米的水平偏移，导致X坐标的相应变化

这个变换矩阵可以用于将墙面上的任意点坐标转换到摄像头坐标系中，进而进行图像投影等后续处理。
