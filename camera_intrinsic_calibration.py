"""
基于墙面角点的摄像头内参标定
使用墙面坐标系下的3D角点和对应的2D图像点来标定摄像头内参
"""

import numpy as np
import cv2
import math
from typing import List, Tuple, Optional
import matplotlib.pyplot as plt
from calculate_wall_to_camera_transform import calculate_wall_to_camera_transform

def calibrate_camera_intrinsics(
    wall_points_3d: np.ndarray,
    image_points_2d: np.ndarray,
    image_size: Tuple[int, int],
    T_wall_to_cam: Optional[np.ndarray] = None,
    initial_guess: Optional[np.ndarray] = None
) -> Tuple[np.ndarray, np.ndarray, float]:
    """
    基于墙面角点标定摄像头内参
    
    参数:
        wall_points_3d: 墙面坐标系下的3D角点，形状为(N, 3)
        image_points_2d: 对应的2D图像点，形状为(N, 2)
        image_size: 图像尺寸(width, height)
        T_wall_to_cam: 墙面到摄像头的变换矩阵(4x4)，如果为None则自动计算
        initial_guess: 内参矩阵的初始猜测值(3x3)
    
    返回:
        camera_matrix: 摄像头内参矩阵(3x3)
        dist_coeffs: 畸变系数
        reprojection_error: 重投影误差
    """
    
    # 1. 如果没有提供变换矩阵，使用默认参数计算
    if T_wall_to_cam is None:
        T_wall_to_cam, _, _, _ = calculate_wall_to_camera_transform()
        print("使用默认参数计算的变换矩阵")
    
    # 2. 将墙面3D点转换到摄像头坐标系
    # 转换为齐次坐标
    wall_points_3d_homo = np.hstack([wall_points_3d, np.ones((wall_points_3d.shape[0], 1))])
    
    # 应用变换矩阵
    cam_points_3d_homo = (T_wall_to_cam @ wall_points_3d_homo.T).T
    cam_points_3d = cam_points_3d_homo[:, :3]  # 去掉齐次坐标
    
    print(f"墙面3D点数量: {len(wall_points_3d)}")
    print(f"图像2D点数量: {len(image_points_2d)}")
    print(f"摄像头坐标系3D点范围:")
    print(f"  X: [{cam_points_3d[:, 0].min():.3f}, {cam_points_3d[:, 0].max():.3f}]")
    print(f"  Y: [{cam_points_3d[:, 1].min():.3f}, {cam_points_3d[:, 1].max():.3f}]")
    print(f"  Z: [{cam_points_3d[:, 2].min():.3f}, {cam_points_3d[:, 2].max():.3f}]")
    
    # 3. 设置初始内参猜测值
    if initial_guess is None:
        # 使用图像尺寸估算初始焦距
        fx = fy = max(image_size) * 0.8  # 经验值
        cx, cy = image_size[0] / 2, image_size[1] / 2
        camera_matrix = np.array([
            [fx, 0, cx],
            [0, fy, cy],
            [0, 0, 1]
        ], dtype=np.float64)
    else:
        camera_matrix = initial_guess.copy()
    
    # 4. 初始畸变系数（假设无畸变）
    dist_coeffs = np.zeros(5, dtype=np.float64)
    
    # 5. 使用OpenCV的calibrateCamera函数
    # 需要将数据组织成OpenCV期望的格式
    object_points = [cam_points_3d.astype(np.float32)]
    image_points = [image_points_2d.astype(np.float32)]
    
    # 标定标志
    flags = (cv2.CALIB_USE_INTRINSIC_GUESS |  # 使用初始猜测值
             cv2.CALIB_FIX_ASPECT_RATIO |     # 固定纵横比
             cv2.CALIB_ZERO_TANGENT_DIST)     # 假设切向畸变为0
    
    # 执行标定
    ret, camera_matrix, dist_coeffs, rvecs, tvecs = cv2.calibrateCamera(
        object_points, image_points, image_size, camera_matrix, dist_coeffs, flags=flags
    )
    
    if not ret:
        raise RuntimeError("摄像头标定失败")
    
    # 6. 计算重投影误差
    reprojected_points, _ = cv2.projectPoints(
        cam_points_3d.astype(np.float32), 
        rvecs[0], tvecs[0], 
        camera_matrix, dist_coeffs
    )
    reprojected_points = reprojected_points.reshape(-1, 2)
    
    reprojection_error = np.sqrt(np.mean(
        np.sum((image_points_2d - reprojected_points) ** 2, axis=1)
    ))
    
    return camera_matrix, dist_coeffs, reprojection_error

def generate_test_data(
    grid_size: Tuple[int, int] = (10, 8),
    wall_size: Tuple[float, float] = (2.0, 1.5),
    image_size: Tuple[int, int] = (1920, 1080),
    noise_level: float = 0.5
) -> Tuple[np.ndarray, np.ndarray]:
    """
    生成测试用的角点数据
    
    参数:
        grid_size: 网格尺寸(列数, 行数)
        wall_size: 墙面实际尺寸(宽度, 高度)，单位米
        image_size: 图像尺寸(width, height)
        noise_level: 图像点噪声水平，单位像素
    
    返回:
        wall_points_3d: 墙面坐标系下的3D点
        image_points_2d: 对应的2D图像点
    """
    
    # 1. 生成墙面3D网格点
    cols, rows = grid_size
    wall_width, wall_height = wall_size
    
    x_coords = np.linspace(-wall_width/2, wall_width/2, cols)
    y_coords = np.linspace(-wall_height/2, wall_height/2, rows)
    
    wall_points_3d = []
    for y in y_coords:
        for x in x_coords:
            wall_points_3d.append([x, y, 0.0])  # Z=0，在墙面上
    
    wall_points_3d = np.array(wall_points_3d, dtype=np.float32)
    
    # 2. 使用理想的摄像头参数生成对应的2D点
    # 假设的真实内参
    fx = fy = 1000.0  # 焦距
    cx, cy = image_size[0] / 2, image_size[1] / 2  # 主点
    true_camera_matrix = np.array([
        [fx, 0, cx],
        [0, fy, cy],
        [0, 0, 1]
    ])
    
    # 获取变换矩阵
    T_wall_to_cam, _, _, _ = calculate_wall_to_camera_transform()
    
    # 转换到摄像头坐标系
    wall_points_3d_homo = np.hstack([wall_points_3d, np.ones((wall_points_3d.shape[0], 1))])
    cam_points_3d_homo = (T_wall_to_cam @ wall_points_3d_homo.T).T
    cam_points_3d = cam_points_3d_homo[:, :3]
    
    # 投影到图像平面
    image_points_2d = []
    for point_3d in cam_points_3d:
        if point_3d[2] > 0:  # 确保点在摄像头前方
            x_img = fx * point_3d[0] / point_3d[2] + cx
            y_img = fy * point_3d[1] / point_3d[2] + cy
            image_points_2d.append([x_img, y_img])
        else:
            # 如果点在摄像头后方，设置为无效点
            image_points_2d.append([0, 0])
    
    image_points_2d = np.array(image_points_2d, dtype=np.float32)
    
    # 3. 添加噪声
    if noise_level > 0:
        noise = np.random.normal(0, noise_level, image_points_2d.shape)
        image_points_2d += noise
    
    # 4. 过滤掉图像边界外的点
    valid_mask = (
        (image_points_2d[:, 0] >= 0) & (image_points_2d[:, 0] < image_size[0]) &
        (image_points_2d[:, 1] >= 0) & (image_points_2d[:, 1] < image_size[1]) &
        (cam_points_3d[:, 2] > 0)  # 确保在摄像头前方
    )
    
    wall_points_3d = wall_points_3d[valid_mask]
    image_points_2d = image_points_2d[valid_mask]
    
    print(f"生成了 {len(wall_points_3d)} 个有效角点")
    print(f"真实内参矩阵:")
    print(true_camera_matrix)
    
    return wall_points_3d, image_points_2d

def visualize_calibration_results(
    wall_points_3d: np.ndarray,
    image_points_2d: np.ndarray,
    camera_matrix: np.ndarray,
    dist_coeffs: np.ndarray,
    image_size: Tuple[int, int],
    T_wall_to_cam: np.ndarray
):
    """
    可视化标定结果
    """
    # 1. 转换3D点到摄像头坐标系
    wall_points_3d_homo = np.hstack([wall_points_3d, np.ones((wall_points_3d.shape[0], 1))])
    cam_points_3d_homo = (T_wall_to_cam @ wall_points_3d_homo.T).T
    cam_points_3d = cam_points_3d_homo[:, :3]

    # 2. 重投影
    rvec = np.zeros(3)  # 因为点已经在摄像头坐标系中
    tvec = np.zeros(3)
    reprojected_points, _ = cv2.projectPoints(
        cam_points_3d.astype(np.float32), rvec, tvec, camera_matrix, dist_coeffs
    )
    reprojected_points = reprojected_points.reshape(-1, 2)

    # 3. 创建可视化图像
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))

    # 3D点分布
    ax = axes[0, 0]
    ax.scatter(wall_points_3d[:, 0], wall_points_3d[:, 1], c='blue', alpha=0.6)
    ax.set_xlabel('X (m)')
    ax.set_ylabel('Y (m)')
    ax.set_title('墙面3D角点分布')
    ax.grid(True)
    ax.axis('equal')

    # 摄像头坐标系3D点
    ax = axes[0, 1]
    ax.scatter(cam_points_3d[:, 0], cam_points_3d[:, 1], c='red', alpha=0.6)
    ax.set_xlabel('X (m)')
    ax.set_ylabel('Y (m)')
    ax.set_title('摄像头坐标系3D点分布')
    ax.grid(True)
    ax.axis('equal')

    # 2D图像点分布
    ax = axes[1, 0]
    ax.scatter(image_points_2d[:, 0], image_points_2d[:, 1], c='blue', alpha=0.6, label='原始点')
    ax.scatter(reprojected_points[:, 0], reprojected_points[:, 1], c='red', alpha=0.6, label='重投影点')
    ax.set_xlabel('X (pixels)')
    ax.set_ylabel('Y (pixels)')
    ax.set_title('2D图像点对比')
    ax.legend()
    ax.grid(True)
    ax.set_xlim(0, image_size[0])
    ax.set_ylim(image_size[1], 0)  # 图像坐标系Y轴向下

    # 重投影误差分布
    ax = axes[1, 1]
    errors = np.sqrt(np.sum((image_points_2d - reprojected_points) ** 2, axis=1))
    ax.hist(errors, bins=20, alpha=0.7, color='green')
    ax.set_xlabel('重投影误差 (pixels)')
    ax.set_ylabel('频次')
    ax.set_title(f'重投影误差分布 (平均: {np.mean(errors):.2f} pixels)')
    ax.grid(True)

    plt.tight_layout()
    plt.savefig('calibration_results.png', dpi=300, bbox_inches='tight')
    print("可视化结果已保存到 'calibration_results.png'")

    # 4. 打印详细结果
    print("\n" + "="*60)
    print("摄像头内参标定结果")
    print("="*60)
    print(f"内参矩阵:")
    print(f"  fx = {camera_matrix[0, 0]:.2f}")
    print(f"  fy = {camera_matrix[1, 1]:.2f}")
    print(f"  cx = {camera_matrix[0, 2]:.2f}")
    print(f"  cy = {camera_matrix[1, 2]:.2f}")
    print(f"\n完整内参矩阵:")
    for i in range(3):
        print(f"  [{camera_matrix[i, 0]:8.2f}, {camera_matrix[i, 1]:8.2f}, {camera_matrix[i, 2]:8.2f}]")

    print(f"\n畸变系数:")
    print(f"  k1 = {dist_coeffs[0]:.6f}")
    print(f"  k2 = {dist_coeffs[1]:.6f}")
    print(f"  p1 = {dist_coeffs[2]:.6f}")
    print(f"  p2 = {dist_coeffs[3]:.6f}")
    print(f"  k3 = {dist_coeffs[4]:.6f}")

    print(f"\n重投影误差统计:")
    print(f"  平均误差: {np.mean(errors):.3f} pixels")
    print(f"  最大误差: {np.max(errors):.3f} pixels")
    print(f"  标准差: {np.std(errors):.3f} pixels")

def main():
    """
    主函数：演示摄像头内参标定流程
    """
    print("摄像头内参标定演示")
    print("="*50)

    # 1. 生成测试数据
    print("\n1. 生成测试角点数据...")
    image_size = (1920, 1080)
    wall_points_3d, image_points_2d = generate_test_data(
        grid_size=(12, 9),
        wall_size=(2.4, 1.8),
        image_size=image_size,
        noise_level=0.3
    )

    # 2. 执行标定
    print("\n2. 执行摄像头内参标定...")
    camera_matrix, dist_coeffs, reprojection_error = calibrate_camera_intrinsics(
        wall_points_3d, image_points_2d, image_size
    )

    print(f"\n标定完成！重投影误差: {reprojection_error:.3f} pixels")

    # 3. 可视化结果
    print("\n3. 生成可视化结果...")
    T_wall_to_cam, _, _, _ = calculate_wall_to_camera_transform()
    visualize_calibration_results(
        wall_points_3d, image_points_2d, camera_matrix, dist_coeffs, image_size, T_wall_to_cam
    )

    # 4. 保存结果
    print("\n4. 保存标定结果...")
    np.savez('camera_intrinsic_params.npz',
             camera_matrix=camera_matrix,
             dist_coeffs=dist_coeffs,
             reprojection_error=reprojection_error,
             image_size=image_size)
    print("标定参数已保存到 camera_intrinsic_params.npz")

if __name__ == "__main__":
    main()
