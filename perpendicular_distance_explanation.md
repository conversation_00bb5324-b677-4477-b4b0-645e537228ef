# 垂直距离约束下的摄像头坐标系变换

## 新的约束条件

### 几何设置
- **摄像头光心到墙的垂直距离 = 1.0米**
- **摄像头pitch角度 = 17°（向上倾斜为正）**
- **摄像头yaw角度 = -2°**
- **水平偏移 = 0.1米**

### 摄像头位置计算

**摄像头在墙坐标系中的位置：[0.100, -0.306, -1.000]**

**计算逻辑：**
1. **Z坐标 = -1.000米**（垂直距离，固定值）
2. **X坐标 = 0.100米**（水平偏移，给定值）
3. **Y坐标 = -1.0 × tan(17°) ≈ -0.306米**（由pitch角决定）

### 几何关系解释

```
侧视图（YZ平面）：

墙面 (Z=0)          摄像头 (Z=-1)
    |                    •
    |                 /  ↑ 17° pitch角
    |              /
    |           /
    |        /
    |     /
    |  /
墙原点 ←—————————————————— 1.0米垂直距离
    |
    |
    ↓ -0.306米
    摄像头Y位置
```

### 为什么需要Y偏移？

当摄像头向上倾斜17°时，为了让光轴能够指向墙面，摄像头必须在Y方向上向下偏移。这个偏移量由几何关系决定：

**Y偏移 = -垂直距离 × tan(pitch角)**
**Y偏移 = -1.0 × tan(17°) ≈ -0.306米**

### 距离关系

1. **垂直距离**：1.000米（Z坐标绝对值）
2. **到墙原点的直线距离**：√(0.1² + (-0.306)² + (-1.0)²) = 1.050米
3. **光轴投影距离**：墙原点在摄像头坐标系中的Z坐标 = 0.870米

### 物理意义

1. **垂直距离约束**更符合实际应用场景
2. **摄像头安装位置**通常以到墙的垂直距离为准
3. **pitch角度的影响**体现在Y方向的偏移上
4. **光轴投影**仍然正确反映了深度信息

### 与之前版本的对比

| 约束条件 | 摄像头位置 | 到墙原点距离 | 墙原点Z坐标 |
|---------|-----------|-------------|------------|
| 直线距离1米 | [0.100, -0.291, -0.952] | 1.000米 | 0.828米 |
| 垂直距离1米 | [0.100, -0.306, -1.000] | 1.050米 | 0.870米 |

### 应用场景

垂直距离约束更适合以下场景：
- 摄像头安装时以到墙的垂直距离为标准
- 需要固定的成像距离
- 标定过程中的几何约束

这种设置下的变换矩阵同样准确，但几何关系更符合实际安装和使用场景。
